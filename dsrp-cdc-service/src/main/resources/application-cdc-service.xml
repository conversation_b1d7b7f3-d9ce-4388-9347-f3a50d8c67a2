<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:reg="http://www.dangdang.com/schema/ddframe/reg"
       xmlns:job="http://www.dangdang.com/schema/ddframe/job"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
       http://www.dangdang.com/schema/ddframe/reg
       http://www.dangdang.com/schema/ddframe/reg/reg.xsd
       http://www.dangdang.com/schema/ddframe/job
       http://www.dangdang.com/schema/ddframe/job/job.xsd">

    <context:annotation-config/>
    <context:component-scan base-package="com.yyigou.dsrp.cdc.service"/>
    <context:component-scan base-package="com.yyigou.ddc.services.dw.doris"/>

    <bean id="redisUtils" class="com.yyigou.ddc.common.cache.redisson3.RedisUtils">
        <constructor-arg ref="redisClientConfig"/>
    </bean>
    <bean id="redisClientConfig" class="com.yyigou.ddc.common.cache.redisson3.config.RedisClientConfig">
        <property name="databaseIndex" value="1"/>
        <property name="masterIP" value="${common.redis5.ip}"/>
        <property name="port" value="${common.redis5.port}"/>
        <property name="retryAttempts" value="${common.redis5.retryAttempts:30}"/>
        <property name="retryInterval" value="${common.redis5.retryInterval:1000}"/>
    </bean>

    <reg:zookeeper id="regCenter" server-lists="${common.jobreg.servers}" namespace="${common.jobreg.namespace}"
                   base-sleep-time-milliseconds="${common.jobreg.baseSleepTimeMilliseconds}"
                   max-sleep-time-milliseconds="${common.jobreg.maxSleepTimeMilliseconds}"
                   max-retries="${common.jobreg.maxRetries}"/>


    <job:simple id="supplier2NCJob" class="com.yyigou.dsrp.cdc.service.job.KlpCustomer2NCJob"
                registry-center-ref="regCenter"
                sharding-total-count="1" cron="0 0/1 * * * ?" overwrite="true"/>
</beans>